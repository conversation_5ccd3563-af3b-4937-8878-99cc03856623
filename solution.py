import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import math

# -----------------------------------------------------------------------------
# Complex-Valued Neural Network Components
# -----------------------------------------------------------------------------

class CardioidActivation(nn.Module):
    """Cardioid activation function for complex-valued neural networks.

    f(z) = 0.5 * (1 + cos(arg(z))) * z

    This activation preserves phase information while providing non-linearity
    based on the phase of the complex input.
    """

    def __init__(self):
        super().__init__()

    def forward(self, z):
        """Apply Cardioid activation to complex tensor.

        Args:
            z: Complex tensor of any shape

        Returns:
            Complex tensor with Cardioid activation applied
        """
        if not torch.is_complex(z):
            # If input is real, treat as complex with zero imaginary part
            z = z.to(torch.complex64)

        # Compute phase angle
        phase = torch.angle(z)

        # Cardioid scaling factor: 0.5 * (1 + cos(phase))
        scale = 0.5 * (1 + torch.cos(phase))

        # Apply scaling while preserving phase
        return scale * z


class ComplexLayerNorm(nn.Module):
    """Complex-valued Layer Normalization.

    Applies layer normalization separately to real and imaginary parts,
    which is a simple but effective approach for complex-valued networks.
    """

    def __init__(self, normalized_shape, eps=1e-6, data_format="channels_last"):
        super().__init__()
        self.real_norm = nn.LayerNorm(normalized_shape, eps=eps)
        self.imag_norm = nn.LayerNorm(normalized_shape, eps=eps)
        self.data_format = data_format

    def forward(self, x):
        """Apply complex layer normalization.

        Args:
            x: Complex tensor

        Returns:
            Normalized complex tensor
        """
        if not torch.is_complex(x):
            return self.real_norm(x)

        # Handle data format conversion for channels_first
        if self.data_format == "channels_first" and x.ndim == 4:
            # Convert from (N, C, H, W) to (N, H, W, C)
            x = x.permute(0, 2, 3, 1)

        # Apply normalization to real and imaginary parts separately
        real_normalized = self.real_norm(x.real)
        imag_normalized = self.imag_norm(x.imag)

        result = torch.complex(real_normalized, imag_normalized)

        # Convert back to channels_first if needed
        if self.data_format == "channels_first" and result.ndim == 4:
            result = result.permute(0, 3, 1, 2)

        return result


# -----------------------------------------------------------------------------
# Complex ConvNeXt Block Implementation
# -----------------------------------------------------------------------------
class ComplexConvNeXtBlock(nn.Module):
    """Complex-valued ConvNeXt Block with complex operations and Cardioid activation."""

    def __init__(self, dim, drop_path=0., layer_scale_init_value=1e-6):
        super().__init__()
        # Complex depthwise convolution
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim, dtype=torch.complex64)

        # Complex layer normalization
        self.norm = ComplexLayerNorm(dim, eps=1e-6)

        # Complex pointwise convolutions (implemented with linear layers)
        self.pwconv1 = nn.Linear(dim, 4 * dim, dtype=torch.complex64)

        # Cardioid activation for complex values
        self.act = CardioidActivation()

        self.pwconv2 = nn.Linear(4 * dim, dim, dtype=torch.complex64)

        # Layer scale parameter (complex)
        if layer_scale_init_value > 0:
            self.gamma = nn.Parameter(
                layer_scale_init_value * torch.ones((dim), dtype=torch.complex64),
                requires_grad=True
            )
        else:
            self.gamma = None

        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x):
        """Forward pass for complex ConvNeXt block.

        Args:
            x: Complex tensor of shape (N, C, H, W)

        Returns:
            Complex tensor of same shape
        """
        input_x = x

        # Depthwise convolution
        x = self.dwconv(x)

        # Permute for layer norm: (N, C, H, W) -> (N, H, W, C)
        x = x.permute(0, 2, 3, 1)

        # Layer normalization
        x = self.norm(x)

        # First pointwise convolution
        x = self.pwconv1(x)

        # Cardioid activation
        x = self.act(x)

        # Second pointwise convolution
        x = self.pwconv2(x)

        # Layer scale
        if self.gamma is not None:
            x = self.gamma * x

        # Permute back: (N, H, W, C) -> (N, C, H, W)
        x = x.permute(0, 3, 1, 2)

        # Residual connection with drop path
        x = input_x + self.drop_path(x)
        return x


class DropPath(nn.Module):
    """Drop paths (Stochastic Depth) per sample (when applied in main path of residual blocks)."""
    def __init__(self, drop_prob=None):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        if self.drop_prob == 0. or not self.training:
            return x
        keep_prob = 1 - self.drop_prob
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)  # work with diff dim tensors, not just 2D ConvNets
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()  # binarize
        output = x.div(keep_prob) * random_tensor
        return output


# -----------------------------------------------------------------------------
# Complex ConvNeXt Encoder Implementation
# -----------------------------------------------------------------------------
class ComplexConvNeXtEncoder(nn.Module):
    """Complex-valued ConvNeXt encoder following ConvNeXt-T architecture."""

    def __init__(self, in_chans=1, depths=[3, 3, 9, 3], dims=[96, 192, 384, 768],
                 drop_path_rate=0., layer_scale_init_value=1e-6):
        super().__init__()

        self.downsample_layers = nn.ModuleList()  # stem and 3 intermediate downsampling conv layers

        # Stem layer - convert input to complex and initial feature extraction
        stem = nn.Sequential(
            nn.Conv2d(in_chans, dims[0], kernel_size=4, stride=4, dtype=torch.complex64),
            ComplexLayerNorm(dims[0], eps=1e-6, data_format="channels_first")
        )
        self.downsample_layers.append(stem)

        # Intermediate downsampling layers
        for i in range(3):
            downsample_layer = nn.Sequential(
                ComplexLayerNorm(dims[i], eps=1e-6, data_format="channels_first"),
                nn.Conv2d(dims[i], dims[i+1], kernel_size=2, stride=2, dtype=torch.complex64),
            )
            self.downsample_layers.append(downsample_layer)

        # Feature extraction stages with complex ConvNeXt blocks
        self.stages = nn.ModuleList()
        dp_rates = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        cur = 0
        for i in range(4):
            stage = nn.Sequential(
                *[ComplexConvNeXtBlock(dim=dims[i], drop_path=dp_rates[cur + j],
                layer_scale_init_value=layer_scale_init_value) for j in range(depths[i])]
            )
            self.stages.append(stage)
            cur += depths[i]

    def forward(self, x):
        """Forward pass for complex encoder.

        Args:
            x: Complex tensor of shape (N, 1, H, W)

        Returns:
            List of complex feature tensors at different scales
        """
        features = []
        for i in range(4):
            x = self.downsample_layers[i](x)
            x = self.stages[i](x)
            features.append(x)
        return features


# Custom LayerNorm for ConvNeXt
class LayerNorm(nn.Module):
    """LayerNorm that supports two data formats: channels_last (default) or channels_first."""

    def __init__(self, normalized_shape, eps=1e-6, data_format="channels_last"):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.bias = nn.Parameter(torch.zeros(normalized_shape))
        self.eps = eps
        self.data_format = data_format
        if self.data_format not in ["channels_last", "channels_first"]:
            raise NotImplementedError
        self.normalized_shape = (normalized_shape, )

    def forward(self, x):
        if self.data_format == "channels_last":
            return F.layer_norm(x, self.normalized_shape, self.weight, self.bias, self.eps)
        elif self.data_format == "channels_first":
            u = x.mean(1, keepdim=True)
            s = (x - u).pow(2).mean(1, keepdim=True)
            x = (x - u) / torch.sqrt(s + self.eps)
            x = self.weight[:, None, None] * x + self.bias[:, None, None]
            return x


# Patch LayerNorm to use custom implementation
nn.LayerNorm = LayerNorm


# -----------------------------------------------------------------------------
# Complex ConvNeXt-UNet Decoder Implementation
# -----------------------------------------------------------------------------
class ComplexConvNeXtUNetDecoder(nn.Module):
    """Complex-valued ConvNeXt-UNet decoder with skip connections for feature reconstruction."""

    def __init__(self, encoder_dims=[96, 192, 384, 768], decoder_dims=[384, 192, 96, 48]):
        super().__init__()

        # Upsampling layers (complex)
        self.upsamples = nn.ModuleList()
        self.decoder_blocks = nn.ModuleList()

        # From bottleneck (768) to decoder_dims[0] (384)
        self.upsamples.append(
            nn.ConvTranspose2d(encoder_dims[3], decoder_dims[0], kernel_size=2, stride=2, dtype=torch.complex64)
        )
        self.decoder_blocks.append(
            nn.Sequential(
                nn.Conv2d(decoder_dims[0] + encoder_dims[2], decoder_dims[0], kernel_size=3, padding=1, dtype=torch.complex64),
                ComplexLayerNorm(decoder_dims[0], data_format="channels_first"),
                CardioidActivation(),
                ComplexConvNeXtBlock(decoder_dims[0])
            )
        )

        # From decoder_dims[0] (384) to decoder_dims[1] (192)
        self.upsamples.append(
            nn.ConvTranspose2d(decoder_dims[0], decoder_dims[1], kernel_size=2, stride=2, dtype=torch.complex64)
        )
        self.decoder_blocks.append(
            nn.Sequential(
                nn.Conv2d(decoder_dims[1] + encoder_dims[1], decoder_dims[1], kernel_size=3, padding=1, dtype=torch.complex64),
                ComplexLayerNorm(decoder_dims[1], data_format="channels_first"),
                CardioidActivation(),
                ComplexConvNeXtBlock(decoder_dims[1])
            )
        )

        # From decoder_dims[1] (192) to decoder_dims[2] (96)
        self.upsamples.append(
            nn.ConvTranspose2d(decoder_dims[1], decoder_dims[2], kernel_size=2, stride=2, dtype=torch.complex64)
        )
        self.decoder_blocks.append(
            nn.Sequential(
                nn.Conv2d(decoder_dims[2] + encoder_dims[0], decoder_dims[2], kernel_size=3, padding=1, dtype=torch.complex64),
                ComplexLayerNorm(decoder_dims[2], data_format="channels_first"),
                CardioidActivation(),
                ComplexConvNeXtBlock(decoder_dims[2])
            )
        )

        # Final upsampling to original resolution
        self.upsamples.append(
            nn.ConvTranspose2d(decoder_dims[2], decoder_dims[3], kernel_size=4, stride=4, dtype=torch.complex64)
        )
        self.decoder_blocks.append(
            nn.Sequential(
                nn.Conv2d(decoder_dims[3], decoder_dims[3], kernel_size=3, padding=1, dtype=torch.complex64),
                ComplexLayerNorm(decoder_dims[3], data_format="channels_first"),
                CardioidActivation()
            )
        )

    def forward(self, encoder_features):
        """Forward pass for complex decoder.

        Args:
            encoder_features: List of complex features from encoder [feat0, feat1, feat2, feat3]
                             where feat3 is the bottleneck feature
        Returns:
            Decoded complex feature map at original resolution
        """
        x = encoder_features[3]  # Start from bottleneck

        # Decode with skip connections
        for i in range(4):
            x = self.upsamples[i](x)
            if i < 3:  # Skip connection for first 3 layers
                skip_feat = encoder_features[2-i]  # Reverse order: feat2, feat1, feat0
                x = torch.cat([x, skip_feat], dim=1)
            x = self.decoder_blocks[i](x)

        return x


# -----------------------------------------------------------------------------
# Vector to Skew-Symmetric Matrix Conversion
# -----------------------------------------------------------------------------
def vector_to_skew_symmetric(vec, matrix_dim):
    """Convert vector to skew-symmetric matrix.
    Args:
        vec: (batch_size, D), where D = M*(M-1)/2
        matrix_dim: M
    Returns:
        skew_matrix: (batch_size, M, M) skew-symmetric matrix
    """
    batch_size = vec.shape[0]
    device = vec.device
    skew_matrix = torch.zeros(batch_size, matrix_dim, matrix_dim, device=device, dtype=vec.dtype)

    # Get upper triangular indices
    triu_indices = torch.triu_indices(matrix_dim, matrix_dim, offset=1, device=device)

    # Populate the upper triangle
    skew_matrix[:, triu_indices[0], triu_indices[1]] = vec

    # Create the skew-symmetric matrix: A - A^T
    return skew_matrix - skew_matrix.transpose(-2, -1)


# -----------------------------------------------------------------------------
# Enhanced Complex Scaled Cayley Transform Module
# -----------------------------------------------------------------------------
class ComplexScaledCayleyTransform(nn.Module):
    """Enhanced Scaled Cayley Transform for generating unitary matrices from skew-Hermitian matrices.

    This implementation supports complex matrices and follows the optimization plan section 2.3.
    The transform is: W = (I - A) @ inv(I + A) @ D
    where A is skew-Hermitian (A^H = -A) and D is a scaling matrix.
    """

    def __init__(self, matrix_dim, num_neg_ones_in_D=0, eps=1e-8, learnable_D=False):
        super().__init__()
        self.matrix_dim = matrix_dim
        self.eps = eps
        self.learnable_D = learnable_D

        if learnable_D:
            # Learnable D matrix: parameterize as complex scaling factors
            # Initialize with unit magnitude and random phases
            self.D_logits = nn.Parameter(torch.randn(matrix_dim, dtype=torch.complex64))
            print(f"[ComplexScaledCayleyTransform] Using learnable complex D matrix with {matrix_dim} parameters")
        else:
            # Fixed D matrix as a non-trainable buffer
            D = torch.ones(matrix_dim, dtype=torch.complex64)
            if num_neg_ones_in_D > 0:
                D[:num_neg_ones_in_D] = -1.0 + 0j
            self.register_buffer('D', torch.diag(D))
            print(f"[ComplexScaledCayleyTransform] Using fixed D matrix with {num_neg_ones_in_D} negative ones")

    def get_D_matrix(self):
        """Get the current D matrix (either fixed or learned)."""
        if self.learnable_D:
            # Convert complex logits to unit magnitude complex numbers
            # This ensures D remains on the unit circle for stability
            D_normalized = self.D_logits / (torch.abs(self.D_logits) + self.eps)
            return torch.diag(D_normalized)
        else:
            return self.D

    def forward(self, A):
        """Apply Enhanced Complex Scaled Cayley Transform: W = (I - A) @ inv(I + A) @ D

        Enhanced with numerical stability improvements for complex matrices:
        - Regularization for matrix inversion
        - Proper handling of skew-Hermitian matrices
        - Double precision computation for critical operations

        Args:
            A: skew-Hermitian matrix (batch_size, M, M) where A^H = -A
        Returns:
            W: unitary matrix (batch_size, M, M) where W^H @ W = I
        """
        batch_size = A.shape[0]
        device = A.device
        dtype = A.dtype

        # Ensure we're working with complex matrices
        if not torch.is_complex(A):
            A = A.to(torch.complex64)
            dtype = torch.complex64

        # Create identity matrix
        Id = torch.eye(self.matrix_dim, device=device, dtype=dtype).expand(batch_size, -1, -1)

        # For numerical stability, use double precision for matrix operations
        if dtype == torch.complex64:
            A_double = A.to(torch.complex128)
            Id_double = Id.to(torch.complex128)

            # Compute (I + A) with regularization for stability
            I_plus_A = Id_double + A_double

            # Add small regularization to diagonal for numerical stability
            reg_term = self.eps * torch.eye(self.matrix_dim, device=device, dtype=torch.complex128)
            I_plus_A = I_plus_A + reg_term.expand_as(I_plus_A)

            # Compute (I - A)
            I_minus_A = Id_double - A_double

            # Solve (I + A) @ Q = (I - A) for Q using stable solver
            try:
                Q = torch.linalg.solve(I_plus_A, I_minus_A)
            except RuntimeError:
                # Fallback to pseudo-inverse if solve fails
                Q = torch.matmul(torch.linalg.pinv(I_plus_A), I_minus_A)

            # Convert back to original precision
            Q = Q.to(dtype)
        else:
            # Direct computation for other dtypes
            I_plus_A = Id + A
            I_minus_A = Id - A

            # Add regularization
            reg_term = self.eps * torch.eye(self.matrix_dim, device=device, dtype=dtype)
            I_plus_A = I_plus_A + reg_term.expand_as(I_plus_A)

            try:
                Q = torch.linalg.solve(I_plus_A, I_minus_A)
            except RuntimeError:
                Q = torch.matmul(torch.linalg.pinv(I_plus_A), I_minus_A)

        # Apply the scaling matrix D (either fixed or learned)
        D_matrix = self.get_D_matrix().to(dtype)
        W = torch.matmul(Q, D_matrix)

        return W

    def check_orthogonality(self, W):
        """Check orthogonality of the output matrix for debugging."""
        WtW = torch.matmul(W.transpose(-2, -1), W)
        I = torch.eye(W.size(-1), device=W.device, dtype=W.dtype)
        error = torch.mean(torch.abs(WtW - I) ** 2)
        return error.item()


# -----------------------------------------------------------------------------
# Enhanced Complex Unitary Matrix Generator
# -----------------------------------------------------------------------------
class ComplexUnitaryGenerator(nn.Module):
    """Enhanced module that converts parameter vectors to unitary matrices with full complex support.

    This implementation follows the optimization plan section 2.3 for generating unitary matrices
    through skew-Hermitian matrix construction and Scaled Cayley Transform.
    """

    def __init__(self, matrix_dim, rank, num_neg_ones_in_D=0, learnable_D=False):
        super().__init__()
        self.matrix_dim = matrix_dim
        self.rank = rank
        self.learnable_D = learnable_D

        # For complex skew-Hermitian matrices: A^H = -A
        # We need matrix_dim^2 real parameters to fully parameterize a skew-Hermitian matrix:
        # - matrix_dim parameters for diagonal (purely imaginary)
        # - matrix_dim*(matrix_dim-1)/2 parameters for upper triangle real parts
        # - matrix_dim*(matrix_dim-1)/2 parameters for upper triangle imaginary parts
        self.param_dim = matrix_dim * matrix_dim

        # Enhanced Complex Cayley transform with configurable D matrix
        self.cayley_transform = ComplexScaledCayleyTransform(
            matrix_dim,
            num_neg_ones_in_D=num_neg_ones_in_D,
            learnable_D=learnable_D
        )

    def forward(self, param_vec):
        """Convert parameter vector to unitary matrix.

        Args:
            param_vec: (batch_size, param_dim) parameter vector
        Returns:
            unitary_mat: (batch_size, matrix_dim, rank, 2) truncated unitary matrix in real-imag format
        """
        batch_size = param_vec.shape[0]

        # Convert vector to skew-Hermitian matrix
        A = self._vector_to_skew_hermitian(param_vec, self.matrix_dim)

        # Apply Complex Scaled Cayley Transform to get unitary matrix
        W = self.cayley_transform(A)  # (batch_size, matrix_dim, matrix_dim)

        # Truncate to get the first R columns
        W_truncated = W[:, :, :self.rank]  # (batch_size, matrix_dim, rank)

        # Convert complex tensor to real-imaginary format for output
        if torch.is_complex(W_truncated):
            W_ri = torch.stack([W_truncated.real, W_truncated.imag], dim=-1)
        else:
            # Fallback: treat as real with zero imaginary part
            W_ri = torch.stack([W_truncated, torch.zeros_like(W_truncated)], dim=-1)

        return W_ri

    def _vector_to_skew_hermitian(self, vec, matrix_dim):
        """Convert vector to skew-Hermitian matrix A where A^H = -A.

        This implementation follows the optimization plan for proper parameterization
        of skew-Hermitian matrices in complex domain.

        Args:
            vec: (batch_size, matrix_dim^2) parameter vector
            matrix_dim: dimension of the output matrix
        Returns:
            skew_hermitian: (batch_size, matrix_dim, matrix_dim) skew-Hermitian matrix
        """
        batch_size = vec.shape[0]
        device = vec.device
        dtype = torch.complex64 if vec.dtype == torch.float32 else torch.complex128

        # Reshape vector to matrix form for easier indexing
        vec_reshaped = vec.view(batch_size, matrix_dim, matrix_dim)

        # Initialize complex matrix
        A = torch.zeros(batch_size, matrix_dim, matrix_dim, dtype=dtype, device=device)

        # Set diagonal elements to purely imaginary (skew-Hermitian constraint)
        diag_indices = torch.arange(matrix_dim, device=device)
        A[:, diag_indices, diag_indices] = 1j * vec_reshaped[:, diag_indices, diag_indices]

        # Get upper triangular indices (excluding diagonal)
        triu_indices = torch.triu_indices(matrix_dim, matrix_dim, offset=1, device=device)

        # For upper triangle, use both real and imaginary parts from parameter vector
        # Split the upper triangle parameters into real and imaginary parts
        n_upper = len(triu_indices[0])
        upper_params = vec_reshaped[:, triu_indices[0], triu_indices[1]]  # (batch, n_upper)

        # Use first half for real parts, second half for imaginary parts
        if upper_params.shape[1] >= 2 * n_upper:
            upper_real = upper_params[:, :n_upper]
            upper_imag = upper_params[:, n_upper:2*n_upper]
        else:
            # Fallback: use all as real parts, zero imaginary
            upper_real = upper_params
            upper_imag = torch.zeros_like(upper_real)

        # Set upper triangle
        A[:, triu_indices[0], triu_indices[1]] = upper_real + 1j * upper_imag

        # Set lower triangle to satisfy skew-Hermitian property: A[j,i] = -conj(A[i,j])
        A[:, triu_indices[1], triu_indices[0]] = -torch.conj(A[:, triu_indices[0], triu_indices[1]])

        return A


# -----------------------------------------------------------------------------
# Optimized Complex SVDNet with Full CVNN Architecture
# -----------------------------------------------------------------------------
class SVDNet(nn.Module):
    """Fully optimized SVDNet following the comprehensive optimization plan.

    Key optimizations implemented:
    - Full complex-valued neural network (CVNN) architecture (Section 3.2)
    - Complex ConvNeXt-UNet backbone with Cardioid activation
    - Enhanced Complex Scaled Cayley Transform for unitary constraints (Section 2.3)
    - Support for uncertainty-based dynamic loss weighting (Section 4.1)
    - Two-stage curriculum learning compatibility (Section 4.2)
    """

    def __init__(self, dim: int = 64, rank: int = 32, weight_path: str = "svdnet.pth",
                 num_neg_ones_in_D: int = 0, learnable_D: bool = False):
        super().__init__()
        self.dim = dim  # Antenna dimension (M == N)
        self.rank = rank

        # --------------------------- Complex ConvNeXt-UNet Backbone ----------------------------
        # Lightweight Complex ConvNeXt encoder (ConvNeXt-T configuration)
        encoder_dims = [96, 192, 384, 768]  # ConvNeXt-T dimensions
        decoder_dims = [384, 192, 96, 48]   # Decoder dimensions

        # Complex encoder with single-channel complex input
        self.encoder = ComplexConvNeXtEncoder(
            in_chans=1,  # Single complex channel
            depths=[3, 3, 9, 3],  # ConvNeXt-T depths
            dims=encoder_dims,
            drop_path_rate=0.1,
            layer_scale_init_value=1e-6
        )

        # Complex ConvNeXt-UNet decoder with skip connections
        self.decoder = ComplexConvNeXtUNetDecoder(
            encoder_dims=encoder_dims,
            decoder_dims=decoder_dims
        )

        # ------------------------ Complex Multi-task Prediction Heads ----------------------------
        # Shared feature dimension from complex decoder
        shared_dim = decoder_dims[3]  # 48

        # Complex U prediction head - generates parameters for unitary matrix U
        self.head_U = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),  # Global average pooling for complex features
            nn.Flatten(),
            nn.Linear(shared_dim, 256, dtype=torch.complex64),
            CardioidActivation(),
            nn.Dropout(0.1),
            nn.Linear(256, dim * dim, dtype=torch.complex64),  # Parameters for complex skew-Hermitian matrix
        )

        # Complex V prediction head - generates parameters for unitary matrix V
        self.head_V = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),  # Global average pooling for complex features
            nn.Flatten(),
            nn.Linear(shared_dim, 256, dtype=torch.complex64),
            CardioidActivation(),
            nn.Dropout(0.1),
            nn.Linear(256, dim * dim, dtype=torch.complex64),  # Parameters for complex skew-Hermitian matrix
        )

        # S prediction head - generates real-valued singular values from complex features
        self.head_S = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),  # Global average pooling for complex features
            nn.Flatten(),
            nn.Linear(shared_dim, 256, dtype=torch.complex64),
            CardioidActivation(),
            nn.Dropout(0.1),
            nn.Linear(256, 128, dtype=torch.complex64),
            CardioidActivation(),
            nn.Linear(128, rank, dtype=torch.complex64),
        )

        # ----------------------- Enhanced Complex Unitary Generators ------------------------
        # Use fully optimized complex unitary generators with Scaled Cayley Transform
        self.generator_U = ComplexUnitaryGenerator(
            dim, rank,
            num_neg_ones_in_D=num_neg_ones_in_D,
            learnable_D=learnable_D
        )
        self.generator_V = ComplexUnitaryGenerator(
            dim, rank,
            num_neg_ones_in_D=num_neg_ones_in_D,
            learnable_D=learnable_D
        )

        # ------------------------ Weight loading ----------------------------
        if os.path.isfile(weight_path):
            state = torch.load(weight_path, map_location="cpu")
            try:
                self.load_state_dict(state, strict=False)
                print(f"[Enhanced SVDNet] Loaded weights from {weight_path} (strict=False)")
            except RuntimeError as e:
                print(f"[Enhanced SVDNet] Weight loading failed: {e}. Proceeding with random init.")

    # ---------------------------------------------------------------------
    def forward(self, x: torch.Tensor):
        """Enhanced forward pass with ConvNeXt-UNet backbone.
        Args:
            x: complex channel, shape [M, N, 2] or [B, M, N, 2]
        Returns:
            U: left unitary matrix [B, M, R, 2] or [M, R, 2]
            S: singular values [B, R] or [R]
            V: right unitary matrix [B, N, R, 2] or [N, R, 2]
        """
        if x.ndim == 3:
            x = x.unsqueeze(0)
        if x.shape[-1] != 2:
            raise ValueError("Input last dim must be 2 (real/imag)")
        B = x.size(0)

        # Convert to complex format for complex neural network processing
        x_complex = to_complex(x)  # [B, M, N] complex

        # Prepare input for Complex ConvNeXt: add channel dimension
        feat = x_complex.unsqueeze(1)  # [B, 1, M, N] complex

        # Complex ConvNeXt encoder: extract hierarchical complex features
        encoder_features = self.encoder(feat)  # List of complex features at different scales

        # Complex ConvNeXt-UNet decoder: reconstruct features with skip connections
        decoded_feat = self.decoder(encoder_features)  # [B, 48, M, N] complex

        # Complex multi-task prediction heads
        # U head: predict complex parameters for unitary matrix U
        U_params_complex = self.head_U(decoded_feat)  # [B, dim*dim] complex

        # V head: predict complex parameters for unitary matrix V
        V_params_complex = self.head_V(decoded_feat)  # [B, dim*dim] complex

        # S head: predict singular values from complex features
        S_complex = self.head_S(decoded_feat)  # [B, rank] complex

        # Extract real part and apply softplus for positive singular values
        S = F.softplus(S_complex.real) + 1e-6  # [B, rank]

        # Generate unitary matrices using Enhanced Complex Scaled Cayley Transform
        # Convert complex parameters to real for the parameter vector input
        U = self.generator_U(U_params_complex.real)  # [B, dim, rank, 2]
        V = self.generator_V(V_params_complex.real)  # [B, dim, rank, 2]

        # Remove batch dim if B==1 (to match demo expectations)
        if B == 1:
            U = U.squeeze(0)
            V = V.squeeze(0)
            S = S.squeeze(0)

        return U, S, V

    # ------------------------------------------------------------------
    @staticmethod
    def _to_complex(mat: torch.Tensor) -> torch.Tensor:
        """Convert real-imag stacked tensor [..., 2] → complex."""
        return torch.complex(mat[..., 0], mat[..., 1])

    @staticmethod
    def _to_ri(mat: torch.Tensor) -> torch.Tensor:
        """Convert complex tensor → real-imag stacked."""
        return torch.stack((mat.real, mat.imag), dim=-1)

    def get_model_complexity(self):
        """Calculate model complexity in terms of parameters and FLOPs."""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024)  # Assuming float32
        }