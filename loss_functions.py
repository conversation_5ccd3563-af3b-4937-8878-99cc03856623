#!/usr/bin/env python3
"""
Multi-Component Loss Functions for SVDNet

This module implements the loss functions aligned with the AE metric as described
in the optimization plan section 3.1.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


def to_complex(t):
    """Convert real-imaginary tensor to complex."""
    return torch.complex(t[..., 0], t[..., 1])


def frobenius_norm_squared(t):
    """Compute squared Frobenius norm for complex tensor."""
    return torch.sum(torch.abs(t) ** 2, dim=(-2, -1))


class HomoscedasticUncertaintyWeighting(nn.Module):
    """
    Homoscedastic uncertainty-based dynamic loss weighting following Kendall & Gal (2017).

    Learns task-dependent uncertainty parameters to automatically balance multiple loss terms.
    The weighting is based on the principle that tasks with higher uncertainty should receive
    lower weights in the overall loss.

    Loss formulation: L_total = Σ_i (1/(2*σ_i^2)) * L_i + (1/2) * log(σ_i^2)
    where σ_i^2 is the learned uncertainty for task i.
    """

    def __init__(self, num_tasks=3, init_log_var=-1.0, min_log_var=-5.0, max_log_var=2.0):
        """
        Args:
            num_tasks: Number of loss components (reconstruction, U-orthogonality, V-orthogonality)
            init_log_var: Initial value for log(σ^2)
            min_log_var: Minimum allowed log variance (for numerical stability)
            max_log_var: Maximum allowed log variance (to prevent degenerate solutions)
        """
        super().__init__()
        self.num_tasks = num_tasks
        self.min_log_var = min_log_var
        self.max_log_var = max_log_var

        # Learnable log-variance parameters for each task
        self.log_vars = nn.Parameter(torch.full((num_tasks,), init_log_var))

        # Task names for logging
        self.task_names = ['reconstruction', 'U_orthogonality', 'V_orthogonality']

    def forward(self, losses):
        """
        Apply uncertainty-based weighting to multiple loss components.

        Args:
            losses: List or tensor of loss values [L_rec, L_orth_u, L_orth_v]
        Returns:
            weighted_loss: Combined weighted loss
            weights: Current task weights (for logging)
            uncertainties: Current uncertainty values (for logging)
        """
        if isinstance(losses, list):
            losses = torch.stack(losses)

        # Clamp log variances for numerical stability
        log_vars_clamped = torch.clamp(self.log_vars, self.min_log_var, self.max_log_var)

        # Compute precision (inverse variance): 1/σ^2 = exp(-log(σ^2))
        precisions = torch.exp(-log_vars_clamped)

        # Compute weighted losses: (1/(2*σ^2)) * L_i
        weighted_losses = 0.5 * precisions * losses

        # Add regularization term: (1/2) * log(σ^2)
        regularization = 0.5 * log_vars_clamped

        # Total loss
        total_loss = torch.sum(weighted_losses + regularization)

        # Compute current weights and uncertainties for logging
        weights = precisions / torch.sum(precisions)  # Normalized weights
        uncertainties = torch.exp(0.5 * log_vars_clamped)  # σ values

        return total_loss, weights, uncertainties

    def get_task_weights(self):
        """Get current normalized task weights."""
        log_vars_clamped = torch.clamp(self.log_vars, self.min_log_var, self.max_log_var)
        precisions = torch.exp(-log_vars_clamped)
        return precisions / torch.sum(precisions)

    def get_uncertainties(self):
        """Get current uncertainty values (σ)."""
        log_vars_clamped = torch.clamp(self.log_vars, self.min_log_var, self.max_log_var)
        return torch.exp(0.5 * log_vars_clamped)

    def log_weights_and_uncertainties(self, logger=None, step=None):
        """Log current weights and uncertainties."""
        weights = self.get_task_weights()
        uncertainties = self.get_uncertainties()

        info_str = "Dynamic Loss Weights: "
        for i, (name, weight, uncertainty) in enumerate(zip(self.task_names, weights, uncertainties)):
            info_str += f"{name}={weight:.4f}(σ={uncertainty:.4f}) "

        if logger is not None and step is not None:
            logger.info(f"Step {step}: {info_str}")
            # Log to tensorboard if available
            if hasattr(logger, 'add_scalar'):
                for i, name in enumerate(self.task_names):
                    logger.add_scalar(f'loss_weights/{name}', weights[i], step)
                    logger.add_scalar(f'uncertainties/{name}', uncertainties[i], step)
        else:
            print(info_str)


class AEAlignedLoss(nn.Module):
    """
    Multi-component loss function precisely aligned with AE metric.

    The AE metric consists of three components:
    1. Normalized reconstruction error
    2. Left singular matrix orthogonality error
    3. Right singular matrix orthogonality error
    """

    def __init__(self, lambda_rec=1.0, lambda_orth_u=0.1, lambda_orth_v=0.1,
                 eps=1e-8, use_normalized_orth=True):
        super().__init__()
        self.lambda_rec = lambda_rec
        self.lambda_orth_u = lambda_orth_u
        self.lambda_orth_v = lambda_orth_v
        self.eps = eps
        self.use_normalized_orth = use_normalized_orth


class EnhancedAEAlignedLoss(nn.Module):
    """
    Enhanced AE-aligned loss with uncertainty-based dynamic weighting.

    Integrates the original AE-aligned loss components with homoscedastic uncertainty
    weighting for automatic task balancing during training.
    """

    def __init__(self, eps=1e-8, use_normalized_orth=True, use_dynamic_weighting=True,
                 init_log_var=-1.0, min_log_var=-5.0, max_log_var=2.0):
        super().__init__()
        self.eps = eps
        self.use_normalized_orth = use_normalized_orth
        self.use_dynamic_weighting = use_dynamic_weighting

        # Dynamic weighting module
        if use_dynamic_weighting:
            self.uncertainty_weighting = HomoscedasticUncertaintyWeighting(
                num_tasks=3,
                init_log_var=init_log_var,
                min_log_var=min_log_var,
                max_log_var=max_log_var
            )
        else:
            # Static weights (fallback)
            self.register_buffer('static_weights', torch.tensor([1.0, 0.1, 0.1]))

        # Track loss components for analysis
        self.last_loss_components = None
        self.last_weights = None

    def forward(self, U, S, V, H_gt):
        """
        Compute enhanced AE-aligned loss with dynamic weighting.

        Args:
            U: Left unitary matrix [B, M, R, 2]
            S: Singular values [B, R]
            V: Right unitary matrix [B, N, R, 2]
            H_gt: Ground truth channel matrix [B, M, N, 2]
        Returns:
            total_loss: Weighted combination of all loss components
            loss_dict: Dictionary with individual loss components and weights
        """
        batch_size = H_gt.shape[0]

        # Convert to complex tensors
        U_complex = to_complex(U)  # [B, M, R]
        V_complex = to_complex(V)  # [B, N, R]
        H_gt_complex = to_complex(H_gt)  # [B, M, N]

        # 1. Reconstruction Loss (aligned with AE metric component 1)
        # Reconstruct: H_pred = U @ diag(S) @ V^H
        S_diag = torch.diag_embed(S)  # [B, R, R]
        H_pred = torch.matmul(torch.matmul(U_complex, S_diag),
                             torch.conj(V_complex).transpose(-2, -1))  # [B, M, N]

        # Normalized reconstruction error
        reconstruction_error = H_pred - H_gt_complex
        rec_loss_numerator = frobenius_norm_squared(reconstruction_error)
        rec_loss_denominator = frobenius_norm_squared(H_gt_complex) + self.eps
        rec_loss = torch.mean(rec_loss_numerator / rec_loss_denominator)

        # 2. U Orthogonality Loss (aligned with AE metric component 2)
        U_H = torch.conj(U_complex).transpose(-2, -1)  # [B, R, M]
        UHU = torch.matmul(U_H, U_complex)  # [B, R, R]
        I_R = torch.eye(U_complex.shape[-1], device=U.device, dtype=U_complex.dtype)
        I_R = I_R.expand(batch_size, -1, -1)

        if self.use_normalized_orth:
            # Normalized orthogonality error
            orth_u_error = UHU - I_R
            orth_u_loss_numerator = frobenius_norm_squared(orth_u_error)
            orth_u_loss_denominator = frobenius_norm_squared(I_R) + self.eps
            orth_u_loss = torch.mean(orth_u_loss_numerator / orth_u_loss_denominator)
        else:
            # Standard orthogonality error
            orth_u_loss = torch.mean(frobenius_norm_squared(UHU - I_R))

        # 3. V Orthogonality Loss (aligned with AE metric component 3)
        V_H = torch.conj(V_complex).transpose(-2, -1)  # [B, R, N]
        VHV = torch.matmul(V_H, V_complex)  # [B, R, R]

        if self.use_normalized_orth:
            # Normalized orthogonality error
            orth_v_error = VHV - I_R
            orth_v_loss_numerator = frobenius_norm_squared(orth_v_error)
            orth_v_loss_denominator = frobenius_norm_squared(I_R) + self.eps
            orth_v_loss = torch.mean(orth_v_loss_numerator / orth_v_loss_denominator)
        else:
            # Standard orthogonality error
            orth_v_loss = torch.mean(frobenius_norm_squared(VHV - I_R))

        # Store individual loss components
        loss_components = torch.stack([rec_loss, orth_u_loss, orth_v_loss])
        self.last_loss_components = loss_components.detach()

        # Apply weighting strategy
        if self.use_dynamic_weighting:
            # Dynamic uncertainty-based weighting
            total_loss, weights, uncertainties = self.uncertainty_weighting(loss_components)
            self.last_weights = weights.detach()

            loss_dict = {
                'total_loss': total_loss,
                'rec_loss': rec_loss,
                'orth_u_loss': orth_u_loss,
                'orth_v_loss': orth_v_loss,
                'weights': weights,
                'uncertainties': uncertainties,
                'weighted_rec_loss': weights[0] * rec_loss,
                'weighted_orth_u_loss': weights[1] * orth_u_loss,
                'weighted_orth_v_loss': weights[2] * orth_v_loss
            }
        else:
            # Static weighting
            weights = self.static_weights
            total_loss = torch.sum(weights * loss_components)
            self.last_weights = weights

            loss_dict = {
                'total_loss': total_loss,
                'rec_loss': rec_loss,
                'orth_u_loss': orth_u_loss,
                'orth_v_loss': orth_v_loss,
                'weights': weights,
                'weighted_rec_loss': weights[0] * rec_loss,
                'weighted_orth_u_loss': weights[1] * orth_u_loss,
                'weighted_orth_v_loss': weights[2] * orth_v_loss
            }

        return total_loss, loss_dict

    def get_current_weights(self):
        """Get current task weights."""
        if self.use_dynamic_weighting:
            return self.uncertainty_weighting.get_task_weights()
        else:
            return self.static_weights

    def log_loss_info(self, logger=None, step=None):
        """Log current loss components and weights."""
        if self.use_dynamic_weighting:
            self.uncertainty_weighting.log_weights_and_uncertainties(logger, step)


class TwoStageCurriculumLearning(nn.Module):
    """
    Two-stage self-paced curriculum learning strategy.

    Stage 1: Reconstruction Warm-up (focus on reconstruction accuracy)
    Stage 2: Geometric Refinement (balance reconstruction and orthogonality)

    The curriculum automatically transitions between stages based on reconstruction
    performance and integrates with uncertainty-based dynamic weighting.
    """

    def __init__(self, warmup_epochs=50, transition_epochs=20,
                 reconstruction_threshold=0.1, min_orthogonality_weight=0.01,
                 max_orthogonality_weight=0.5, use_adaptive_transition=True):
        """
        Args:
            warmup_epochs: Number of epochs for reconstruction warm-up stage
            transition_epochs: Number of epochs for gradual transition
            reconstruction_threshold: Reconstruction loss threshold for stage transition
            min_orthogonality_weight: Minimum weight for orthogonality losses in stage 1
            max_orthogonality_weight: Maximum weight for orthogonality losses in stage 2
            use_adaptive_transition: Whether to use adaptive transition based on performance
        """
        super().__init__()
        self.warmup_epochs = warmup_epochs
        self.transition_epochs = transition_epochs
        self.reconstruction_threshold = reconstruction_threshold
        self.min_orthogonality_weight = min_orthogonality_weight
        self.max_orthogonality_weight = max_orthogonality_weight
        self.use_adaptive_transition = use_adaptive_transition

        # Current curriculum state
        self.current_epoch = 0
        self.current_stage = 1  # 1: warmup, 2: transition, 3: refinement
        self.stage_progress = 0.0  # Progress within current stage [0, 1]

        # Performance tracking for adaptive transition
        self.reconstruction_loss_history = []
        self.moving_avg_window = 10

        # Enhanced loss function with dynamic weighting
        self.enhanced_loss = EnhancedAEAlignedLoss(
            use_dynamic_weighting=True,
            init_log_var=-2.0,  # Start with lower uncertainty
            min_log_var=-6.0,
            max_log_var=1.0
        )

    def update_epoch(self, epoch):
        """Update current epoch and curriculum stage."""
        self.current_epoch = epoch
        self._update_stage()

    def _update_stage(self):
        """Update curriculum stage based on epoch and performance."""
        if self.current_epoch < self.warmup_epochs:
            self.current_stage = 1  # Reconstruction warm-up
            self.stage_progress = self.current_epoch / self.warmup_epochs
        elif self.current_epoch < self.warmup_epochs + self.transition_epochs:
            self.current_stage = 2  # Transition
            transition_progress = (self.current_epoch - self.warmup_epochs) / self.transition_epochs
            self.stage_progress = transition_progress
        else:
            self.current_stage = 3  # Geometric refinement
            self.stage_progress = 1.0

        # Adaptive transition based on reconstruction performance
        if self.use_adaptive_transition and len(self.reconstruction_loss_history) >= self.moving_avg_window:
            recent_avg = sum(self.reconstruction_loss_history[-self.moving_avg_window:]) / self.moving_avg_window
            if recent_avg < self.reconstruction_threshold and self.current_stage == 1:
                # Early transition to stage 2 if reconstruction is good enough
                self.current_stage = 2
                print(f"[Curriculum] Early transition to stage 2 at epoch {self.current_epoch} "
                      f"(reconstruction loss: {recent_avg:.6f})")

    def get_curriculum_weights(self):
        """Get curriculum-adjusted weights for loss components."""
        if self.current_stage == 1:
            # Stage 1: Focus heavily on reconstruction
            rec_weight = 1.0
            orth_weight = self.min_orthogonality_weight
        elif self.current_stage == 2:
            # Stage 2: Gradual transition
            rec_weight = 1.0
            orth_weight = (self.min_orthogonality_weight +
                          self.stage_progress * (self.max_orthogonality_weight - self.min_orthogonality_weight))
        else:
            # Stage 3: Full geometric refinement
            rec_weight = 1.0
            orth_weight = self.max_orthogonality_weight

        return torch.tensor([rec_weight, orth_weight, orth_weight])

    def forward(self, U, S, V, H_gt):
        """
        Compute curriculum-aware loss.

        Args:
            U, S, V: SVD components
            H_gt: Ground truth channel matrix
        Returns:
            total_loss: Curriculum-weighted loss
            loss_dict: Detailed loss information including curriculum state
        """
        # Get base loss from enhanced AE-aligned loss
        base_loss, base_loss_dict = self.enhanced_loss(U, S, V, H_gt)

        # Track reconstruction loss for adaptive transition
        rec_loss_value = base_loss_dict['rec_loss'].item()
        self.reconstruction_loss_history.append(rec_loss_value)
        if len(self.reconstruction_loss_history) > 100:  # Keep last 100 values
            self.reconstruction_loss_history.pop(0)

        # Apply curriculum weighting
        if self.current_stage == 1:
            # Stage 1: Suppress orthogonality losses during warm-up
            curriculum_weights = self.get_curriculum_weights()

            # Override dynamic weights with curriculum weights during warm-up
            loss_components = torch.stack([
                base_loss_dict['rec_loss'],
                base_loss_dict['orth_u_loss'],
                base_loss_dict['orth_v_loss']
            ])

            curriculum_loss = torch.sum(curriculum_weights * loss_components)

            # Update loss dict with curriculum information
            loss_dict = base_loss_dict.copy()
            loss_dict.update({
                'total_loss': curriculum_loss,
                'curriculum_stage': self.current_stage,
                'stage_progress': self.stage_progress,
                'curriculum_weights': curriculum_weights,
                'base_dynamic_loss': base_loss
            })

            return curriculum_loss, loss_dict

        else:
            # Stage 2 & 3: Use dynamic weighting with curriculum guidance
            # Modify the uncertainty weighting initialization based on curriculum stage
            if self.current_stage == 2:
                # Gradually increase orthogonality importance
                curriculum_guidance = self.get_curriculum_weights()
                # Apply soft guidance to dynamic weights (not hard override)
                loss_dict = base_loss_dict.copy()
                loss_dict.update({
                    'curriculum_stage': self.current_stage,
                    'stage_progress': self.stage_progress,
                    'curriculum_guidance': curriculum_guidance
                })
            else:
                # Stage 3: Full dynamic weighting
                loss_dict = base_loss_dict.copy()
                loss_dict.update({
                    'curriculum_stage': self.current_stage,
                    'stage_progress': self.stage_progress
                })

            return base_loss, loss_dict

    def get_stage_info(self):
        """Get current curriculum stage information."""
        stage_names = {1: "Reconstruction Warm-up", 2: "Transition", 3: "Geometric Refinement"}
        return {
            'stage': self.current_stage,
            'stage_name': stage_names[self.current_stage],
            'progress': self.stage_progress,
            'epoch': self.current_epoch
        }

    def log_curriculum_info(self, logger=None, step=None):
        """Log curriculum learning state."""
        stage_info = self.get_stage_info()
        weights = self.get_curriculum_weights()

        info_str = (f"Curriculum Stage {stage_info['stage']}: {stage_info['stage_name']} "
                   f"(Progress: {stage_info['progress']:.2f}, "
                   f"Weights: rec={weights[0]:.3f}, orth_u={weights[1]:.3f}, orth_v={weights[2]:.3f})")

        if logger is not None and step is not None:
            logger.info(f"Epoch {self.current_epoch}: {info_str}")
            if hasattr(logger, 'add_scalar'):
                logger.add_scalar('curriculum/stage', self.current_stage, step)
                logger.add_scalar('curriculum/progress', self.stage_progress, step)
                logger.add_scalar('curriculum/rec_weight', weights[0], step)
                logger.add_scalar('curriculum/orth_weight', weights[1], step)
        else:
            print(info_str)
        """
        Compute multi-component loss aligned with AE metric.
        
        Args:
            U: Left unitary matrix [B, M, R, 2]
            S: Singular values [B, R]
            V: Right unitary matrix [B, N, R, 2]
            H_gt: Ground truth channel [B, M, N, 2]
            
        Returns:
            Dictionary containing individual loss components and total loss
        """
        # Convert to complex tensors
        U_c = to_complex(U)
        V_c = to_complex(V)
        S_c = S.type(torch.complex64)
        H_gt_c = to_complex(H_gt)
        
        # 1. Normalized reconstruction error (primary component)
        recon_loss = self._reconstruction_loss(U_c, S_c, V_c, H_gt_c)
        
        # 2. Orthogonality losses (auxiliary regularization)
        orth_loss_u = self._orthogonality_loss(U_c, "U")
        orth_loss_v = self._orthogonality_loss(V_c, "V")
        
        # 3. Total weighted loss
        total_loss = (self.lambda_rec * recon_loss + 
                     self.lambda_orth_u * orth_loss_u + 
                     self.lambda_orth_v * orth_loss_v)
        
        return {
            'total_loss': total_loss,
            'reconstruction_loss': recon_loss,
            'orthogonality_loss_u': orth_loss_u,
            'orthogonality_loss_v': orth_loss_v,
            'ae_approximation': recon_loss + orth_loss_u + orth_loss_v  # Direct AE approximation
        }
    
    def _reconstruction_loss(self, U_c, S_c, V_c, H_gt_c):
        """
        Compute normalized reconstruction error exactly as in AE metric.
        
        AE_recon = ||H_label - U*S*V^H||_F / ||H_label||_F
        """
        # Reconstruct channel: H_pred = U @ diag(S) @ V^H
        U_S = U_c * S_c.unsqueeze(1)  # Broadcasting singular values
        H_pred = torch.matmul(U_S, torch.conj(V_c).transpose(-2, -1))
        
        # Compute normalized reconstruction error
        diff = H_pred - H_gt_c
        numerator = frobenius_norm_squared(diff)  # [B]
        denominator = frobenius_norm_squared(H_gt_c) + self.eps  # [B]
        
        normalized_error = numerator / denominator
        return torch.mean(normalized_error)
    
    def _orthogonality_loss(self, matrix, name):
        """
        Compute orthogonality loss: ||U^H @ U - I||_F^2
        
        Args:
            matrix: Unitary matrix [B, M, R] (complex)
            name: Matrix name for debugging
        """
        # Compute U^H @ U
        matrix_H = torch.conj(matrix).transpose(-2, -1)
        product = torch.matmul(matrix_H, matrix)  # [B, R, R]
        
        # Create identity matrix
        R = matrix.size(-1)
        I = torch.eye(R, device=matrix.device, dtype=matrix.dtype)
        I = I.expand_as(product)
        
        # Compute ||U^H @ U - I||_F^2
        diff = product - I
        orth_error = torch.mean(torch.abs(diff) ** 2, dim=(-2, -1))  # [B]
        
        if self.use_normalized_orth:
            # Normalize by matrix size for stability
            orth_error = orth_error / (R * R)
        
        return torch.mean(orth_error)


class AdaptiveLoss(nn.Module):
    """
    Adaptive loss function that adjusts weights based on training progress.
    """
    
    def __init__(self, initial_lambda_rec=1.0, initial_lambda_orth_u=0.1, 
                 initial_lambda_orth_v=0.1, adaptation_rate=0.01):
        super().__init__()
        self.base_loss = AEAlignedLoss(initial_lambda_rec, initial_lambda_orth_u, initial_lambda_orth_v)
        self.adaptation_rate = adaptation_rate
        self.step_count = 0
        
        # Learnable weight parameters
        self.log_lambda_orth_u = nn.Parameter(torch.log(torch.tensor(initial_lambda_orth_u)))
        self.log_lambda_orth_v = nn.Parameter(torch.log(torch.tensor(initial_lambda_orth_v)))
    
    def forward(self, U, S, V, H_gt):
        """Forward pass with adaptive weights."""
        self.step_count += 1
        
        # Update adaptive weights
        self.base_loss.lambda_orth_u = torch.exp(self.log_lambda_orth_u)
        self.base_loss.lambda_orth_v = torch.exp(self.log_lambda_orth_v)
        
        return self.base_loss(U, S, V, H_gt)


class RobustLoss(nn.Module):
    """
    Robust loss function with Huber-like behavior for outlier resistance.
    """
    
    def __init__(self, lambda_rec=1.0, lambda_orth_u=0.1, lambda_orth_v=0.1, 
                 huber_delta=1.0):
        super().__init__()
        self.lambda_rec = lambda_rec
        self.lambda_orth_u = lambda_orth_u
        self.lambda_orth_v = lambda_orth_v
        self.huber_delta = huber_delta
    
    def forward(self, U, S, V, H_gt):
        """Forward pass with robust loss."""
        # Get standard loss components
        base_loss = AEAlignedLoss(self.lambda_rec, self.lambda_orth_u, self.lambda_orth_v)
        loss_dict = base_loss(U, S, V, H_gt)
        
        # Apply Huber-like robustness to reconstruction loss
        recon_loss = loss_dict['reconstruction_loss']
        if recon_loss > self.huber_delta:
            robust_recon_loss = self.huber_delta * (2 * torch.sqrt(recon_loss / self.huber_delta) - 1)
        else:
            robust_recon_loss = recon_loss
        
        # Recompute total loss with robust reconstruction term
        total_loss = (self.lambda_rec * robust_recon_loss + 
                     self.lambda_orth_u * loss_dict['orthogonality_loss_u'] + 
                     self.lambda_orth_v * loss_dict['orthogonality_loss_v'])
        
        loss_dict['total_loss'] = total_loss
        loss_dict['robust_reconstruction_loss'] = robust_recon_loss
        
        return loss_dict


def compute_ae_metric(U, S, V, H_gt):
    """
    Compute the exact AE metric as defined in the competition.
    
    AE = ||H_label - U*S*V^H||_F / ||H_label||_F + ||U^H*U - I||_F + ||V^H*V - I||_F
    
    Args:
        U: Left unitary matrix [B, M, R, 2] or [M, R, 2]
        S: Singular values [B, R] or [R]
        V: Right unitary matrix [B, N, R, 2] or [N, R, 2]
        H_gt: Ground truth channel [B, M, N, 2] or [M, N, 2]
        
    Returns:
        AE metric value
    """
    # Ensure batch dimension
    if U.ndim == 3:
        U = U.unsqueeze(0)
        S = S.unsqueeze(0)
        V = V.unsqueeze(0)
        H_gt = H_gt.unsqueeze(0)
    
    # Convert to complex
    U_c = to_complex(U)
    V_c = to_complex(V)
    S_c = S.type(torch.complex64)
    H_gt_c = to_complex(H_gt)
    
    # 1. Normalized reconstruction error
    U_S = U_c * S_c.unsqueeze(1)
    H_pred = torch.matmul(U_S, torch.conj(V_c).transpose(-2, -1))
    
    recon_error = torch.norm(H_pred - H_gt_c, p='fro', dim=(-2, -1))
    h_norm = torch.norm(H_gt_c, p='fro', dim=(-2, -1))
    normalized_recon_error = recon_error / (h_norm + 1e-8)
    
    # 2. U orthogonality error
    U_H = torch.conj(U_c).transpose(-2, -1)
    UHU = torch.matmul(U_H, U_c)
    I_U = torch.eye(U_c.size(-1), device=U_c.device, dtype=U_c.dtype)
    orth_error_U = torch.norm(UHU - I_U, p='fro', dim=(-2, -1))
    
    # 3. V orthogonality error
    V_H = torch.conj(V_c).transpose(-2, -1)
    VHV = torch.matmul(V_H, V_c)
    I_V = torch.eye(V_c.size(-1), device=V_c.device, dtype=V_c.dtype)
    orth_error_V = torch.norm(VHV - I_V, p='fro', dim=(-2, -1))
    
    # Total AE
    ae = normalized_recon_error + orth_error_U + orth_error_V
    
    return torch.mean(ae)


if __name__ == "__main__":
    # Test the loss functions
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # Create test data
    B, M, N, R = 4, 64, 64, 32
    U = torch.randn(B, M, R, 2, device=device)
    S = torch.rand(B, R, device=device)
    V = torch.randn(B, N, R, 2, device=device)
    H_gt = torch.randn(B, M, N, 2, device=device)
    
    # Test AE-aligned loss
    loss_fn = AEAlignedLoss().to(device)
    loss_dict = loss_fn(U, S, V, H_gt)
    
    print("Loss components:")
    for key, value in loss_dict.items():
        print(f"  {key}: {value.item():.6f}")
    
    # Test AE metric computation
    ae_value = compute_ae_metric(U, S, V, H_gt)
    print(f"\nAE metric: {ae_value.item():.6f}")
