#!/usr/bin/env python3
"""
Multi-Component Loss Functions for SVDNet

This module implements the loss functions aligned with the AE metric as described
in the optimization plan section 3.1.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


def to_complex(t):
    """Convert real-imaginary tensor to complex."""
    return torch.complex(t[..., 0], t[..., 1])


def frobenius_norm_squared(t):
    """Compute squared Frobenius norm for complex tensor."""
    return torch.sum(torch.abs(t) ** 2, dim=(-2, -1))


class AEAlignedLoss(nn.Module):
    """
    Multi-component loss function precisely aligned with AE metric.
    
    The AE metric consists of three components:
    1. Normalized reconstruction error
    2. Left singular matrix orthogonality error  
    3. Right singular matrix orthogonality error
    """
    
    def __init__(self, lambda_rec=1.0, lambda_orth_u=0.1, lambda_orth_v=0.1, 
                 eps=1e-8, use_normalized_orth=True):
        super().__init__()
        self.lambda_rec = lambda_rec
        self.lambda_orth_u = lambda_orth_u
        self.lambda_orth_v = lambda_orth_v
        self.eps = eps
        self.use_normalized_orth = use_normalized_orth
    
    def forward(self, U, S, V, H_gt):
        """
        Compute multi-component loss aligned with AE metric.
        
        Args:
            U: Left unitary matrix [B, M, R, 2]
            S: Singular values [B, R]
            V: Right unitary matrix [B, N, R, 2]
            H_gt: Ground truth channel [B, M, N, 2]
            
        Returns:
            Dictionary containing individual loss components and total loss
        """
        # Convert to complex tensors
        U_c = to_complex(U)
        V_c = to_complex(V)
        S_c = S.type(torch.complex64)
        H_gt_c = to_complex(H_gt)
        
        # 1. Normalized reconstruction error (primary component)
        recon_loss = self._reconstruction_loss(U_c, S_c, V_c, H_gt_c)
        
        # 2. Orthogonality losses (auxiliary regularization)
        orth_loss_u = self._orthogonality_loss(U_c, "U")
        orth_loss_v = self._orthogonality_loss(V_c, "V")
        
        # 3. Total weighted loss
        total_loss = (self.lambda_rec * recon_loss + 
                     self.lambda_orth_u * orth_loss_u + 
                     self.lambda_orth_v * orth_loss_v)
        
        return {
            'total_loss': total_loss,
            'reconstruction_loss': recon_loss,
            'orthogonality_loss_u': orth_loss_u,
            'orthogonality_loss_v': orth_loss_v,
            'ae_approximation': recon_loss + orth_loss_u + orth_loss_v  # Direct AE approximation
        }
    
    def _reconstruction_loss(self, U_c, S_c, V_c, H_gt_c):
        """
        Compute normalized reconstruction error exactly as in AE metric.
        
        AE_recon = ||H_label - U*S*V^H||_F / ||H_label||_F
        """
        # Reconstruct channel: H_pred = U @ diag(S) @ V^H
        U_S = U_c * S_c.unsqueeze(1)  # Broadcasting singular values
        H_pred = torch.matmul(U_S, torch.conj(V_c).transpose(-2, -1))
        
        # Compute normalized reconstruction error
        diff = H_pred - H_gt_c
        numerator = frobenius_norm_squared(diff)  # [B]
        denominator = frobenius_norm_squared(H_gt_c) + self.eps  # [B]
        
        normalized_error = numerator / denominator
        return torch.mean(normalized_error)
    
    def _orthogonality_loss(self, matrix, name):
        """
        Compute orthogonality loss: ||U^H @ U - I||_F^2
        
        Args:
            matrix: Unitary matrix [B, M, R] (complex)
            name: Matrix name for debugging
        """
        # Compute U^H @ U
        matrix_H = torch.conj(matrix).transpose(-2, -1)
        product = torch.matmul(matrix_H, matrix)  # [B, R, R]
        
        # Create identity matrix
        R = matrix.size(-1)
        I = torch.eye(R, device=matrix.device, dtype=matrix.dtype)
        I = I.expand_as(product)
        
        # Compute ||U^H @ U - I||_F^2
        diff = product - I
        orth_error = torch.mean(torch.abs(diff) ** 2, dim=(-2, -1))  # [B]
        
        if self.use_normalized_orth:
            # Normalize by matrix size for stability
            orth_error = orth_error / (R * R)
        
        return torch.mean(orth_error)


class UncertaintyBasedDynamicLoss(nn.Module):
    """
    Dynamic loss function based on homoscedastic uncertainty weighting.

    This implementation follows the optimization plan section 4.1 for automatic
    multi-task balancing using learnable uncertainty parameters.

    The loss function is:
    L_total = (1/σ_rec²) * L_rec + (1/σ_orth_u²) * L_orth_u + (1/σ_orth_v²) * L_orth_v
              + log(σ_rec) + log(σ_orth_u) + log(σ_orth_v)
    """

    def __init__(self, initial_sigma_rec=1.0, initial_sigma_orth_u=1.0,
                 initial_sigma_orth_v=1.0, eps=1e-8):
        super().__init__()
        self.eps = eps

        # Learnable uncertainty parameters (log-parameterized for numerical stability)
        # σ_i represents the model's uncertainty about task i
        self.log_sigma_rec_sq = nn.Parameter(torch.log(torch.tensor(initial_sigma_rec**2)))
        self.log_sigma_orth_u_sq = nn.Parameter(torch.log(torch.tensor(initial_sigma_orth_u**2)))
        self.log_sigma_orth_v_sq = nn.Parameter(torch.log(torch.tensor(initial_sigma_orth_v**2)))

        print(f"[UncertaintyBasedDynamicLoss] Initialized with learnable uncertainty parameters")
        print(f"  Initial σ_rec = {initial_sigma_rec:.3f}")
        print(f"  Initial σ_orth_u = {initial_sigma_orth_u:.3f}")
        print(f"  Initial σ_orth_v = {initial_sigma_orth_v:.3f}")

    def get_uncertainty_weights(self):
        """Get current uncertainty-based weights."""
        sigma_rec_sq = torch.exp(self.log_sigma_rec_sq)
        sigma_orth_u_sq = torch.exp(self.log_sigma_orth_u_sq)
        sigma_orth_v_sq = torch.exp(self.log_sigma_orth_v_sq)

        return {
            'weight_rec': 1.0 / (sigma_rec_sq + self.eps),
            'weight_orth_u': 1.0 / (sigma_orth_u_sq + self.eps),
            'weight_orth_v': 1.0 / (sigma_orth_v_sq + self.eps),
            'sigma_rec': torch.sqrt(sigma_rec_sq),
            'sigma_orth_u': torch.sqrt(sigma_orth_u_sq),
            'sigma_orth_v': torch.sqrt(sigma_orth_v_sq)
        }

    def forward(self, U, S, V, H_gt):
        """Forward pass with uncertainty-based dynamic weighting."""
        # Compute individual loss components
        recon_loss = self._reconstruction_loss(U, S, V, H_gt)
        orth_loss_u = self._orthogonality_loss(U, "U")
        orth_loss_v = self._orthogonality_loss(V, "V")

        # Get current uncertainty weights
        weights = self.get_uncertainty_weights()

        # Compute weighted losses (uncertainty-based weighting)
        weighted_recon = weights['weight_rec'] * recon_loss
        weighted_orth_u = weights['weight_orth_u'] * orth_loss_u
        weighted_orth_v = weights['weight_orth_v'] * orth_loss_v

        # Regularization terms (prevent σ from becoming too large)
        reg_rec = 0.5 * self.log_sigma_rec_sq
        reg_orth_u = 0.5 * self.log_sigma_orth_u_sq
        reg_orth_v = 0.5 * self.log_sigma_orth_v_sq

        # Total loss with uncertainty-based weighting and regularization
        total_loss = (weighted_recon + weighted_orth_u + weighted_orth_v +
                     reg_rec + reg_orth_u + reg_orth_v)

        return {
            'total_loss': total_loss,
            'reconstruction_loss': recon_loss,
            'orthogonality_loss_u': orth_loss_u,
            'orthogonality_loss_v': orth_loss_v,
            'weighted_reconstruction_loss': weighted_recon,
            'weighted_orthogonality_loss_u': weighted_orth_u,
            'weighted_orthogonality_loss_v': weighted_orth_v,
            'uncertainty_weights': weights,
            'ae_approximation': recon_loss + orth_loss_u + orth_loss_v
        }

    def _reconstruction_loss(self, U, S, V, H_gt):
        """Compute normalized reconstruction error exactly as in AE metric."""
        # Convert to complex tensors
        U_c = to_complex(U)
        V_c = to_complex(V)
        S_c = S.type(torch.complex64)
        H_gt_c = to_complex(H_gt)

        # Reconstruct channel: H_pred = U @ diag(S) @ V^H
        U_S = U_c * S_c.unsqueeze(1)  # Broadcasting singular values
        H_pred = torch.matmul(U_S, torch.conj(V_c).transpose(-2, -1))

        # Compute normalized reconstruction error
        diff = H_pred - H_gt_c
        numerator = frobenius_norm_squared(diff)  # [B]
        denominator = frobenius_norm_squared(H_gt_c) + self.eps  # [B]

        normalized_error = numerator / denominator
        return torch.mean(normalized_error)

    def _orthogonality_loss(self, matrix, name):
        """Compute orthogonality loss: ||U^H @ U - I||_F^2"""
        # Convert to complex
        matrix_c = to_complex(matrix)

        # Compute U^H @ U
        matrix_H = torch.conj(matrix_c).transpose(-2, -1)
        product = torch.matmul(matrix_H, matrix_c)  # [B, R, R]

        # Create identity matrix
        R = matrix_c.size(-1)
        I = torch.eye(R, device=matrix_c.device, dtype=matrix_c.dtype)
        I = I.expand_as(product)

        # Compute ||U^H @ U - I||_F^2
        diff = product - I
        orth_error = torch.mean(torch.abs(diff) ** 2, dim=(-2, -1))  # [B]

        return torch.mean(orth_error)


class RobustLoss(nn.Module):
    """
    Robust loss function with Huber-like behavior for outlier resistance.
    """
    
    def __init__(self, lambda_rec=1.0, lambda_orth_u=0.1, lambda_orth_v=0.1, 
                 huber_delta=1.0):
        super().__init__()
        self.lambda_rec = lambda_rec
        self.lambda_orth_u = lambda_orth_u
        self.lambda_orth_v = lambda_orth_v
        self.huber_delta = huber_delta
    
    def forward(self, U, S, V, H_gt):
        """Forward pass with robust loss."""
        # Get standard loss components
        base_loss = AEAlignedLoss(self.lambda_rec, self.lambda_orth_u, self.lambda_orth_v)
        loss_dict = base_loss(U, S, V, H_gt)
        
        # Apply Huber-like robustness to reconstruction loss
        recon_loss = loss_dict['reconstruction_loss']
        if recon_loss > self.huber_delta:
            robust_recon_loss = self.huber_delta * (2 * torch.sqrt(recon_loss / self.huber_delta) - 1)
        else:
            robust_recon_loss = recon_loss
        
        # Recompute total loss with robust reconstruction term
        total_loss = (self.lambda_rec * robust_recon_loss + 
                     self.lambda_orth_u * loss_dict['orthogonality_loss_u'] + 
                     self.lambda_orth_v * loss_dict['orthogonality_loss_v'])
        
        loss_dict['total_loss'] = total_loss
        loss_dict['robust_reconstruction_loss'] = robust_recon_loss
        
        return loss_dict


def compute_ae_metric(U, S, V, H_gt):
    """
    Compute the exact AE metric as defined in the competition.
    
    AE = ||H_label - U*S*V^H||_F / ||H_label||_F + ||U^H*U - I||_F + ||V^H*V - I||_F
    
    Args:
        U: Left unitary matrix [B, M, R, 2] or [M, R, 2]
        S: Singular values [B, R] or [R]
        V: Right unitary matrix [B, N, R, 2] or [N, R, 2]
        H_gt: Ground truth channel [B, M, N, 2] or [M, N, 2]
        
    Returns:
        AE metric value
    """
    # Ensure batch dimension
    if U.ndim == 3:
        U = U.unsqueeze(0)
        S = S.unsqueeze(0)
        V = V.unsqueeze(0)
        H_gt = H_gt.unsqueeze(0)
    
    # Convert to complex
    U_c = to_complex(U)
    V_c = to_complex(V)
    S_c = S.type(torch.complex64)
    H_gt_c = to_complex(H_gt)
    
    # 1. Normalized reconstruction error
    U_S = U_c * S_c.unsqueeze(1)
    H_pred = torch.matmul(U_S, torch.conj(V_c).transpose(-2, -1))
    
    recon_error = torch.norm(H_pred - H_gt_c, p='fro', dim=(-2, -1))
    h_norm = torch.norm(H_gt_c, p='fro', dim=(-2, -1))
    normalized_recon_error = recon_error / (h_norm + 1e-8)
    
    # 2. U orthogonality error
    U_H = torch.conj(U_c).transpose(-2, -1)
    UHU = torch.matmul(U_H, U_c)
    I_U = torch.eye(U_c.size(-1), device=U_c.device, dtype=U_c.dtype)
    orth_error_U = torch.norm(UHU - I_U, p='fro', dim=(-2, -1))
    
    # 3. V orthogonality error
    V_H = torch.conj(V_c).transpose(-2, -1)
    VHV = torch.matmul(V_H, V_c)
    I_V = torch.eye(V_c.size(-1), device=V_c.device, dtype=V_c.dtype)
    orth_error_V = torch.norm(VHV - I_V, p='fro', dim=(-2, -1))
    
    # Total AE
    ae = normalized_recon_error + orth_error_U + orth_error_V
    
    return torch.mean(ae)


if __name__ == "__main__":
    # Test the loss functions
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # Create test data
    B, M, N, R = 4, 64, 64, 32
    U = torch.randn(B, M, R, 2, device=device)
    S = torch.rand(B, R, device=device)
    V = torch.randn(B, N, R, 2, device=device)
    H_gt = torch.randn(B, M, N, 2, device=device)
    
    # Test AE-aligned loss
    loss_fn = AEAlignedLoss().to(device)
    loss_dict = loss_fn(U, S, V, H_gt)
    
    print("Loss components:")
    for key, value in loss_dict.items():
        print(f"  {key}: {value.item():.6f}")
    
    # Test AE metric computation
    ae_value = compute_ae_metric(U, S, V, H_gt)
    print(f"\nAE metric: {ae_value.item():.6f}")
